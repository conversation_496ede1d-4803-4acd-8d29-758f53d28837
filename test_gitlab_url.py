#!/usr/bin/env python3
"""
Test script for GitLab URL detection and parsing
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from core.git.utils import PlatformDetector, GitUrlParser

def test_url_detection():
    """Test URL detection for various GitLab URLs"""
    
    test_urls = [
        "https://127.0.0.1/seven123/seven-gitlab-demo",
        "https://gitlab.com/user/repo",
        "https://gitlab.example.com/user/repo",
        "https://*************/user/repo",
        "https://localhost:8080/user/repo",
        "git@127.0.0.1:user/repo.git",
    ]
    
    print("🔍 Testing URL Detection and Parsing")
    print("=" * 50)
    
    for url in test_urls:
        print(f"\n📋 Testing URL: {url}")
        
        try:
            # Test platform detection
            platform = PlatformDetector.detect_platform(url)
            print(f"✅ Detected platform: {platform}")

            # Test GitLab URL detection specifically
            is_gitlab = GitUrlParser.is_gitlab_url(url)
            print(f"✅ Is GitLab URL: {is_gitlab}")

            # Test GitHub URL detection
            is_github = GitUrlParser.is_github_url(url)
            print(f"✅ Is GitHub URL: {is_github}")

        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")

if __name__ == "__main__":
    test_url_detection()
